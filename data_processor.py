"""
数据处理和分析模块
"""
import pandas as pd
from typing import List, Dict, Any
from loguru import logger

from models import Campaign, SimilarityData, FinalResult
from wb_api_client import WildberriesAPIClient
from database import db_manager


class KeywordAnalyzer:
    """关键词分析器"""
    
    def __init__(self, api_client: WildberriesAPIClient):
        self.api_client = api_client
    
    def analyze_campaign(self, campaign: Campaign) -> List[FinalResult]:
        """分析单个广告活动"""
        logger.info(f"开始分析广告活动 {campaign.campaign_id}")
        
        try:
            # 1. 获取关键词
            keywords = self.api_client.get_campaign_keywords(campaign.campaign_id)
            if not keywords:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有关键词")
                return []
            
            # 2. 获取关键词统计数据
            keyword_datas = self.api_client.get_keyword_datas_monthly(campaign.campaign_id)

            # 3. 获取产品列表
            products = self.api_client.get_campaign_products(campaign.campaign_id)
            if not products:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有产品")
                return []
            
            # 4. 批量获取数据库中相关度数据
            keyword_product_pairs = []
            for keyword in keywords:
                for product in products:
                    keyword_product_pairs.append((keyword, str(product.nm_id)))
            
            similarity_data = db_manager.batch_get_similarity_data(keyword_product_pairs)
            
            # 5. 组装最终结果
            results = []
            for keyword in keywords:
             
                for product in products:
                    # 使用清理后的关键词查询相关度数据
                    key = (clean_keyword, str(product.nm_id))
                    similarity = similarity_data.get(key)

                    if similarity is None:
                        # 如果没有相关度数据，创建默认值
                        similarity = SimilarityData(
                            keyword=clean_keyword,
                            target_product_id=str(product.nm_id),
                            avg_similarity=0.0,
                            similar_count=0,
                            competitor_count=0,
                            valid_scores=0
                        )

                    # 获取统计数据（使用清理后的关键词）
                    stats_data = stats_dict.get(clean_keyword, {})

                    result = FinalResult(
                        campaign_id=campaign.campaign_id,
                        nm_id=product.nm_id,
                        keyword=keyword.keyword,  # 保留原始关键词（包含[排除]标记）
                        avg_similarity=similarity.avg_similarity,
                        similar_count=similarity.similar_count,
                        competitor_count=similarity.competitor_count,
                        valid_scores=similarity.valid_scores,
                        views=stats_data.get('views', 0),
                        sum=stats_data.get('sum', 0.0),
                        clicks=stats_data.get('clicks', 0),
                        ctr=stats_data.get('ctr', 0.0),
                        count=keyword.count
                    )
                    results.append(result)
            
            logger.info(f"广告活动 {campaign.campaign_id} 分析完成，生成 {len(results)} 条结果")
            return results
            
        except Exception as e:
            logger.error(f"分析广告活动 {campaign.campaign_id} 失败: {e}")
            return []
    
    def analyze_multiple_campaigns(self, campaigns: List[Campaign]) -> List[FinalResult]:
        """分析多个广告活动"""
        all_results = []
        
        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"处理第 {i}/{len(campaigns)} 个广告活动: {campaign.campaign_id}")
            
            try:
                results = self.analyze_campaign(campaign)
                all_results.extend(results)
            except Exception as e:
                logger.error(f"处理广告活动 {campaign.campaign_id} 时发生错误: {e}")
                continue
        
        logger.info(f"所有广告活动分析完成，总共生成 {len(all_results)} 条结果")
        return all_results


class DataExporter:
    """数据导出器"""
    
    @staticmethod
    def to_dataframe(results: List[FinalResult]) -> pd.DataFrame:
        """将结果转换为DataFrame"""
        if not results:
            return pd.DataFrame()
        
        data = [result.to_dict() for result in results]
        df = pd.DataFrame(data)
        
        # 重新排序列
        columns_order = [
            'campaign_id', 'nm_id', 'keyword',
            'avg_similarity', 'similar_count', 'competitor_count', 'valid_scores',
            'views', 'sum', 'clicks', 'ctr', 'count'
        ]
        
        df = df[columns_order]
        return df
    
    @staticmethod
    def to_csv(results: List[FinalResult], filename: str) -> bool:
        """导出为CSV文件"""
        try:
            df = DataExporter.to_dataframe(results)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"数据已导出到 {filename}")
            return True
        except Exception as e:
            logger.error(f"导出CSV文件失败: {e}")
            return False
    
    @staticmethod
    def to_excel(results: List[FinalResult], filename: str) -> bool:
        """导出为Excel文件"""
        try:
            df = DataExporter.to_dataframe(results)
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已导出到 {filename}")
            return True
        except Exception as e:
            logger.error(f"导出Excel文件失败: {e}")
            return False


class KeywordEfficiencyAnalyzer:
    """关键词效益分析器"""
    
    @staticmethod
    def calculate_efficiency_score(result: FinalResult) -> float:
        """计算关键词效益得分"""
        # 基础得分计算逻辑
        similarity_score = result.avg_similarity / 100.0  # 相似度得分 (0-1)
        
        # CTR得分 (点击率越高越好)
        ctr_score = min(result.ctr / 10.0, 1.0)  # 假设10%为满分
        
        # 转化效率得分 (点击数/花费)
        conversion_score = 0.0
        if result.sum > 0:
            conversion_score = min(result.clicks / result.sum * 100, 1.0)
        
        # 相关数量得分
        relevance_score = min(result.similar_count / 10.0, 1.0)  # 假设10个相关为满分
        
        # 综合得分 (加权平均)
        efficiency_score = (
            similarity_score * 0.4 +  # 相似度权重40%
            ctr_score * 0.3 +         # CTR权重30%
            conversion_score * 0.2 +   # 转化效率权重20%
            relevance_score * 0.1      # 相关数量权重10%
        )
        
        return efficiency_score
    
    @staticmethod
    def classify_keywords(results: List[FinalResult]) -> Dict[str, List[FinalResult]]:
        """对关键词进行分类"""
        high_efficiency = []  # 高效益关键词 - 保持不变
        medium_efficiency = []  # 中等效益关键词 - 固定关键词
        low_efficiency = []  # 低效益关键词 - 排除关键词
        
        for result in results:
            score = KeywordEfficiencyAnalyzer.calculate_efficiency_score(result)
            
            if score >= 0.7:
                high_efficiency.append(result)
            elif score >= 0.4:
                medium_efficiency.append(result)
            else:
                low_efficiency.append(result)
        
        return {
            'high_efficiency': high_efficiency,
            'medium_efficiency': medium_efficiency,
            'low_efficiency': low_efficiency
        }
    
    @staticmethod
    def generate_recommendations(classified_results: Dict[str, List[FinalResult]]) -> Dict[str, Any]:
        """生成操作建议"""
        recommendations = {
            'keep_unchanged': [],  # 保持不变
            'fix_keywords': [],   # 固定关键词
            'exclude_keywords': []  # 排除关键词
        }
        
        # 高效益关键词 - 保持不变
        for result in classified_results['high_efficiency']:
            recommendations['keep_unchanged'].append({
                'campaign_id': result.campaign_id,
                'keyword': result.keyword,
                'reason': f'高效益关键词 (相似度: {result.avg_similarity:.1f}%, CTR: {result.ctr:.2f}%)',
                'efficiency_score': KeywordEfficiencyAnalyzer.calculate_efficiency_score(result)
            })
        
        # 中等效益关键词 - 固定关键词
        for result in classified_results['medium_efficiency']:
            recommendations['fix_keywords'].append({
                'campaign_id': result.campaign_id,
                'keyword': result.keyword,
                'reason': f'中等效益关键词 (相似度: {result.avg_similarity:.1f}%, CTR: {result.ctr:.2f}%)',
                'efficiency_score': KeywordEfficiencyAnalyzer.calculate_efficiency_score(result)
            })
        
        # 低效益关键词 - 排除关键词
        for result in classified_results['low_efficiency']:
            recommendations['exclude_keywords'].append({
                'campaign_id': result.campaign_id,
                'keyword': result.keyword,
                'reason': f'低效益关键词 (相似度: {result.avg_similarity:.1f}%, CTR: {result.ctr:.2f}%)',
                'efficiency_score': KeywordEfficiencyAnalyzer.calculate_efficiency_score(result)
            })
        
        return recommendations

    @staticmethod
    def analyze_and_classify(results: List[FinalResult]) -> Dict[str, Any]:
        """分析并分类关键词，返回建议"""
        classified = KeywordEfficiencyAnalyzer.classify_keywords(results)
        recommendations = KeywordEfficiencyAnalyzer.generate_recommendations(classified)
        return recommendations
