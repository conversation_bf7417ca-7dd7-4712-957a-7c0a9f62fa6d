# Wildberries广告关键词效益分析系统

## 项目简介

这是一个自动化的Wildberries广告关键词分析系统，用于分析广告活动中关键词的效益，并自动决定关键词的处理策略（保持不变、固定关键词、排除关键词）。

## 功能特性

- 🔍 **自动获取广告活动**: 通过Wildberries API获取所有广告活动
- 🎯 **智能筛选**: 自动筛选拍卖广告（type=9, status=9）
- 📊 **关键词分析**: 获取关键词数据和统计信息
- 🗄️ **数据库集成**: 从PostgreSQL数据库获取关键词相关度数据
- 📈 **效益评估**: 基于多维度指标计算关键词效益得分
- 📋 **自动分类**: 将关键词分为高效益、中等效益、低效益三类
- 📄 **报告生成**: 生成CSV、Excel和JSON格式的分析报告
- 🔄 **多账号支持**: 支持多个API密钥的负载均衡

## 系统架构

```
wb_adv/
├── config.py              # 配置管理
├── models.py              # 数据模型
├── logger.py              # 日志配置
├── wb_api_client.py       # Wildberries API客户端
├── database.py            # 数据库操作
├── data_processor.py      # 数据处理和分析
├── main.py               # 主程序
├── test_connections.py   # 连接测试
├── requirements.txt      # 依赖包
├── .env                 # 环境变量配置
└── README.md            # 项目说明
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件并配置以下变量：

```env
# Wildberries API密钥（多个密钥用逗号分隔）
WB_API_KEYS=your_api_key_1,your_api_key_2

# PostgreSQL数据库配置
PG_HOST=your_db_host
PG_PORT=5432
PG_USER=your_db_user
PG_PASSWORD=your_db_password
PG_DB=your_db_name
```

### 3. 数据库准备

确保PostgreSQL数据库中存在相关度数据表：
```sql
-- 表结构示例
CREATE TABLE pj_similar.product_analyze_similar_result (
    keyword VARCHAR(255),
    target_product_id VARCHAR(50),
    avg_similarity DECIMAL(5,2),
    similar_count INTEGER,
    competitor_count INTEGER,
    valid_scores INTEGER
);
```

## 使用方法

### 1. 测试连接

首先运行连接测试，确保API和数据库连接正常：

```bash
python test_connections.py
```

### 2. 运行分析

执行主程序开始分析：

```bash
python main.py
```

### 3. 查看结果

程序会生成以下文件：
- `keyword_analysis_YYYYMMDD_HHMMSS.csv` - 原始分析数据
- `keyword_analysis_YYYYMMDD_HHMMSS.xlsx` - Excel格式报告
- `keyword_recommendations_YYYYMMDD_HHMMSS.json` - 关键词操作建议
- `analysis_summary_YYYYMMDD_HHMMSS.json` - 统计摘要

## 数据流程

1. **获取广告活动** → 调用 `/adv/v1/promotion/count` API
2. **筛选拍卖广告** → 过滤 type=9 且 status=9 的广告
3. **获取关键词** → 调用 `/adv/v1/stat/words` API
4. **获取统计数据** → 调用 `/adv/v0/stats/keywords` API（按月分段获取）
5. **获取产品信息** → 调用 `/adv/v1/promotion/adverts` API
6. **查询相关度数据** → 从PostgreSQL数据库批量查询
7. **计算效益得分** → 基于相似度、CTR、转化率等指标
8. **生成建议** → 分类关键词并生成操作建议

## 效益评估算法

关键词效益得分基于以下指标计算：

- **相似度得分** (40%权重): `avg_similarity / 100`
- **CTR得分** (30%权重): `min(ctr / 10, 1.0)`
- **转化效率得分** (20%权重): `min(clicks / sum * 100, 1.0)`
- **相关数量得分** (10%权重): `min(similar_count / 10, 1.0)`

### 分类标准

- **高效益关键词** (≥0.7分): 保持不变
- **中等效益关键词** (0.4-0.7分): 固定关键词
- **低效益关键词** (<0.4分): 排除关键词

## 输出文件说明

### CSV/Excel报告字段

| 字段名 | 说明 |
|--------|------|
| campaign_id | 广告活动ID |
| nm_id | 商品ID |
| keyword | 关键词 |
| avg_similarity | 平均相似度 |
| similar_count | 相似商品数量 |
| competitor_count | 竞争对手数量 |
| valid_scores | 有效评分数 |
| views | 展示次数 |
| sum | 花费金额 |
| clicks | 点击次数 |
| ctr | 点击率 |
| count | 关键词热度 |

### JSON建议文件结构

```json
{
  "keep_unchanged": [
    {
      "campaign_id": 12345,
      "keyword": "高效关键词",
      "reason": "高效益关键词 (相似度: 85.0%, CTR: 5.20%)",
      "efficiency_score": 0.756
    }
  ],
  "fix_keywords": [...],
  "exclude_keywords": [...]
}
```

## API限制和注意事项

- **请求频率**: 建议每个请求间隔1-2秒
- **数据范围**: 关键词统计数据最多获取7天，需要分段获取月度数据
- **重试机制**: 内置指数退避重试机制
- **多账号**: 支持多个API密钥轮换使用

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API请求频率是否过高

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正常运行
   - 验证用户权限

3. **没有获取到关键词**
   - 确认广告活动状态正确
   - 检查广告活动是否设置了关键词
   - 尝试增加处理的广告活动数量

### 日志文件

程序运行时会在 `logs/` 目录下生成详细的日志文件，可用于问题诊断。

## 开发和扩展

### 添加新的分析指标

1. 在 `models.py` 中扩展数据模型
2. 在 `data_processor.py` 中修改效益计算算法
3. 更新报告生成逻辑

### 自定义分类规则

修改 `KeywordEfficiencyAnalyzer.classify_keywords()` 方法中的分类阈值。

### 添加新的数据源

1. 在 `wb_api_client.py` 中添加新的API调用方法
2. 在 `database.py` 中添加新的数据库查询方法
3. 更新数据处理流程

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系
